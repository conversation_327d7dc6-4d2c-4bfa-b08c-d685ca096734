package joe.trader.client;

import java.io.IOException;
import java.math.BigDecimal;

import joe.trader.client.markets.EXCHANGE;
import joe.trader.client.symbol.SYMBOL;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public interface Signal {

   OkHttpClient client = new OkHttpClient();
   boolean isOkToBuy(SYMBOL symbol, EXCHANGE exchange, BigDecimal price);
   boolean isOkToSell(SYMBOL symbol, EXCHANGE exchange, BigDecimal price);

   default String get(String url) throws IOException {
      Request request = new Request.Builder()
          .url(url)
          .build();

      try (Response response = client.newCall(request).execute()) {
        assert response.body() != null;
        return response.body().string();
      }
   }
}
