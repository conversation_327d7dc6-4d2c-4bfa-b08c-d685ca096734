package joe.trader.client;

import java.awt.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;

import joe.trader.client.dto.AssetBuyDTO;
import joe.trader.client.dto.AssetSellDTO;
import joe.trader.client.symbol.SYMBOL;
import joe.trader.client.markets.BinanceExchange;
import joe.trader.client.markets.ByBitExchange;
import joe.trader.entity.DbOrder;
import joe.trader.repo.DbOrderRepo;
import joe.trader.util.Util;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RequiredArgsConstructor
public class Trader {

  private final BinanceExchange binanceMarket;
  private final ByBitExchange bybitMarket;
  private final DbOrderRepo dbOrderRepo;
  private final Util util;
  private final SYMBOL symbol;

  @Getter
  protected int minUSD = 10;

  @Getter
  private final BigDecimal stepSize = new BigDecimal("0.001");

  @Value("${joe.params.dryOrders}")
  private boolean dryOrders;

  @Value("${joe.params.useMarketPrice}")
  private boolean useMarketPrice;

  public DbOrder buy( BigDecimal price, BigDecimal usdValue) {

    // Calculate initial quantity
    BigDecimal quantity = usdValue.divide(price, 8, RoundingMode.HALF_UP);

    if (usdValue.compareTo(new BigDecimal(minUSD)) < 0) {
      log.info("Order too small");
      return null;
    }

    // Normalize quantity according to step size
    BigDecimal normalizedQuantity = util.getNormalizedQuantity(quantity, stepSize);

    Map<String, Object> parameters = new LinkedHashMap<>();
    parameters.put("symbol", symbol.getPair());
    parameters.put("recvWindow", 5000);
    parameters.put("timestamp", System.currentTimeMillis());
    parameters.put("side", "BUY");
    parameters.put("type", "LIMIT");
    parameters.put("timeInForce", "GTC");
    parameters.put("quantity", normalizedQuantity);
    parameters.put("qty", normalizedQuantity);
    parameters.put("price", price);

    log.info(">>> Creating buy order (dry={}) with price {} quantity {} usdValue {}", dryOrders, price, normalizedQuantity, usdValue);
    DbOrder dbOrder = DbOrder.builder()
        .usdValue(usdValue)
        .price(price)
        .quantity(normalizedQuantity)
        .asset(symbol.getName())
        .side("BUY")
        .timestamp(new Date())
        .build();
    dbOrderRepo.save(dbOrder);
    if (!dryOrders) {
      log.warn(">>> BUY Order created {}", dbOrder);
      if( bybitMarket.isUseThisExchange()) {
        bybitMarket.getBybitClient().createOrder(parameters, System.out::println);
      }
      if( binanceMarket.isUseThisExchange()) {
        binanceMarket.getBinanceClient().createTrade().newOrder(parameters);
      }
      Toolkit.getDefaultToolkit().beep();
    }
    return dbOrder;
  }

  public DbOrder sell(DbOrder openOrder, BigDecimal currentPrice) {

    // Normalize quantity according to step size
    BigDecimal normalizedQuantity = util.getNormalizedQuantity(openOrder.getQuantity(), stepSize);
    openOrder.setQuantity(normalizedQuantity);

    Map<String, Object> parameters = new LinkedHashMap<>();
    parameters.put("symbol", symbol.getPair());
    parameters.put("recvWindow", 5000);
    parameters.put("timestamp", System.currentTimeMillis());
    parameters.put("side", "SELL");
    parameters.put("type", "LIMIT");
    parameters.put("quantity", normalizedQuantity);
    parameters.put("qty", normalizedQuantity);

    if (useMarketPrice) {
      parameters.put("type", "MARKET");
    } else {
      parameters.put("type", "LIMIT");
      parameters.put("timeInForce", "GTC");
      parameters.put("price", currentPrice);
    }

    BigDecimal currentValue = normalizedQuantity.multiply(currentPrice);
    openOrder.setSoldPrice(currentPrice);
    openOrder.setSoldTimeStamp(new Date());
    openOrder.setSoldUSDValue(currentPrice.multiply(openOrder.getQuantity()));
    openOrder.setDelta(currentPrice.subtract(openOrder.getPrice()));
    dbOrderRepo.save(openOrder);

    log.info(">>> Creating sell order (dry={}) with price {} quantity {} usdValue {}", dryOrders, currentPrice, normalizedQuantity, currentValue);
    if (!dryOrders) {
      log.warn(">>> SELL Order created {} dry {}", openOrder, false);
      if( bybitMarket.isUseThisExchange()) {
        bybitMarket.getBybitClient().createOrder(parameters, System.out::println);
      }
      if( binanceMarket.isUseThisExchange()) {
        binanceMarket.getBinanceClient().createTrade().newOrder(parameters);
      }
      Toolkit.getDefaultToolkit().beep();
      Toolkit.getDefaultToolkit().beep();
    }
    return openOrder;
  }

  public String buyerTask() {
    log.info("******************* Running Buyer task");
    AssetBuyDTO buyDTOBinance = binanceMarket.checkBuyOrder(symbol);
    log.info("buyDTOBinance: {}", buyDTOBinance);
    AssetBuyDTO buyDTOBybit = bybitMarket.checkBuyOrder(symbol);
    log.info("buyDTOBybit: {}", buyDTOBybit);
    return "Binance: %s Bybit: %s".formatted(buyDTOBinance, buyDTOBybit);
  }

  public String sellerTask() {
    log.info("******************* Running Seller task");
    AssetSellDTO sellDTOBinance = binanceMarket.checkSellOrder(symbol);
    log.info("sellDTOBinance: {}", sellDTOBinance);
    AssetSellDTO sellDTOBybit = bybitMarket.checkSellOrder(symbol);
    log.info("sellDTOBybit: {}", sellDTOBybit);
    return "Binance: %s Bybit: %s".formatted(sellDTOBinance, sellDTOBybit);
  }


}