package joe.trader.client.markets;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.bybit.api.client.domain.CategoryType;
import com.bybit.api.client.domain.account.AccountType;
import com.bybit.api.client.domain.asset.request.AssetDataRequest;
import com.bybit.api.client.domain.market.request.MarketDataRequest;
import com.bybit.api.client.impl.BybitApiAccountRestClientImpl;
import com.bybit.api.client.impl.BybitApiAssetRestClientImpl;
import com.bybit.api.client.impl.BybitApiMarketRestClientImpl;
import com.bybit.api.client.impl.BybitApiTradeAsyncRestClientImpl;
import com.bybit.api.client.service.BybitApiClientFactory;

import joe.trader.client.dto.AssetBuyDTO;
import joe.trader.client.dto.AssetSellDTO;
import joe.trader.client.symbol.SYMBOL;
import joe.trader.entity.DbPrice;
import joe.trader.entity.DbStatus;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ByBitExchange extends ExchangeDef {

  private EXCHANGE exchange = EXCHANGE.BYBIT;

  @Getter
  private BybitApiTradeAsyncRestClientImpl bybitClient;

  @Getter
  private BybitApiAccountRestClientImpl bybitAccountClient;

  @Getter
  private BybitApiAssetRestClientImpl bybitAssetClient;

  @Getter
  private BybitApiMarketRestClientImpl bybitMarketClient;

  @Value("${bybit.api.key}")
  private String bybitApiKey;

  @Value("${bybit.api.secret}")
  private String bybitSecretKey;

  @Getter
  private int usdAvailable = 100;

  public void init() {
    bybitClient = (BybitApiTradeAsyncRestClientImpl) BybitApiClientFactory.newInstance(bybitApiKey, bybitSecretKey).newAsyncTradeRestClient();
    bybitAccountClient = (BybitApiAccountRestClientImpl) BybitApiClientFactory.newInstance(bybitApiKey, bybitSecretKey).newAccountRestClient();
    bybitAssetClient = (BybitApiAssetRestClientImpl) BybitApiClientFactory.newInstance(bybitApiKey, bybitSecretKey).newAssetRestClient();
    bybitMarketClient = (BybitApiMarketRestClientImpl) BybitApiClientFactory.newInstance(bybitApiKey, bybitSecretKey).newMarketDataRestClient();

    initialized = true;
    log.info("Bybit API client initialized successfully");
  }

  @Override
  public AssetBuyDTO checkBuyOrder(SYMBOL symbol) {

    if (!isUseThisExchange()) {
      log.debug("Not using bybit.");
      return null;
    }
    if (!isInit()) {
      throw new IllegalStateException("Spot client not initialized. Call init() first.");
    }

    DbStatus dbStatus = new DbStatus(exchange);
    BigDecimal latestBalance = getLatestBalance();
    BigDecimal previousBalance = (BigDecimal) util.ifNull(dbStatusRepo.getPreviousBalance(), latestBalance);
    log.info("latestBalance: {} previousBalance: {}", latestBalance, previousBalance);
    DbPrice dbPrice = getAssetPrice(symbol);
    priceSignal.setDbPrice(dbPrice);

    BigDecimal currentPrice = dbPrice.getPrice();
    //Persist
    dbStatus.setBalance(latestBalance);
    dbStatus.setPreviousBalance(previousBalance);
    dbStatusRepo.save(dbStatus);

    boolean rsiBuy = rsiSignal.isOkToBuy(symbol, exchange, currentPrice);
    boolean maBuy = maSignal.isOkToBuy(symbol, exchange, currentPrice);
    boolean macdBuy = macdSignal.isOkToBuy(symbol, exchange, currentPrice);
    boolean priceBuy = priceSignal.isOkToBuy(symbol, exchange, currentPrice);
    boolean isUptrend = rsiSignal.isUptrend(symbol, exchange);

    boolean signal4 = maBuy && macdBuy;
    boolean buy = false;

    if (isAlwaysBuy() || rsiBuy || signal4) {

      if (openOrder(exchange, symbol) == null && isUptrend && priceBuy) {
        buy = true;
        getTraderMap().get(symbol).buy(currentPrice, new BigDecimal(usdAvailable));
      } else {
        log.info("Not buying because isUptrend: {} buyPrice: {} or openOrder: {}", isUptrend, priceBuy, openOrder(exchange, symbol));
      }
    }

    return AssetBuyDTO.builder().asset(symbol.getName())
        .rsiBuy(rsiBuy)
        .maBuy(maBuy)
        .macdBuy(macdBuy)
        .priceBuy(priceBuy)
        .uptrend(isUptrend)
        .alwaysBuy(isAlwaysBuy())
        .buy(buy)
        .buyReason("FLAGS RSI: %s MA: %s MACD: %s PRICE: %s UPTREND: %s ALWAYSBUY: %s".formatted(rsiBuy, maBuy, macdBuy, priceBuy, isUptrend, isAlwaysBuy()))
        .balance(latestBalance)
        .price(currentPrice)
        .lowPrice(dbPrice.getLowPrice())
        .highPrice(dbPrice.getHighPrice())
        .exchange(exchange.name())
        .build();
  }

  @Override
  public AssetSellDTO checkSellOrder(SYMBOL symbol) {
    log.warn("Not implemented");
    return null;
  }

  @Override
  public BigDecimal getLatestBalance() {
    final var info = bybitAssetClient.getAssetAllCoinsBalance(AssetDataRequest.builder().accountType(AccountType.UNIFIED).coin("USDT").build());

    BigDecimal walletBalance = getFromResults(info, "result", "balance", "WalletBalance", "walletBalance");

    log.info("Wallet balance: {}", walletBalance);
    return walletBalance;
  }

  @Override
  public DbPrice getAssetPrice(SYMBOL symbol) {

    if ("USDC".equals(symbol.getName()) || "USDT".equals(symbol.getName())) {
      return DbPrice.builder().price(BigDecimal.ONE).lowPrice(BigDecimal.ONE).highPrice(BigDecimal.ONE).build();
    }

    final var info = bybitMarketClient.getMarketTickers(MarketDataRequest.builder()
        .symbol(symbol.getPair2())
        .category(CategoryType.SPOT)
        .build());

    BigDecimal price = getFromResults(info, "result", "list", "lastPrice");
    BigDecimal lowPrice = getFromResults(info, "result", "list", "lowPrice24h");
    BigDecimal highPrice = getFromResults(info, "result", "list", "highPrice24h");

    log.info("Current price for {}: {}", symbol, price);
    log.info("Low price for {}: {}", symbol, lowPrice);
    log.info("High price for {}: {}", symbol, highPrice);

    return util.logR(DbPrice.builder()
        .asset(symbol.getName())
        .price(price)
        .lowPrice(lowPrice)
        .highPrice(highPrice)
        .timestamp(new Date())
        .build());
  }

  private BigDecimal getFromResults(Object info, String... path) {


      Map<?,?> map = (Map<?,?>)info;
      List<?> list = null;

      for(String pathPart : path)
      {
        Object inside1 = map.get(pathPart);
        if(inside1 instanceof List<?>)
        {
          list = (List<?>)inside1;
          map = (Map<?,?>)list.get(0);
          break;
        }
        map = (Map<?,?>)inside1;
      }

      return new BigDecimal(map.get(path[path.length-1]).toString());
  }
}
