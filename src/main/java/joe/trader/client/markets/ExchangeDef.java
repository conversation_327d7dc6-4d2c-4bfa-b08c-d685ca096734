package joe.trader.client.markets;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;

import jakarta.annotation.PostConstruct;
import joe.trader.client.Trader;
import joe.trader.client.dto.AssetBuyDTO;
import joe.trader.client.dto.AssetSellDTO;
import joe.trader.client.signals.MACDSignal;
import joe.trader.client.signals.MASignal;
import joe.trader.client.signals.PriceSignal;
import joe.trader.client.signals.RSISignal;
import joe.trader.client.symbol.SYMBOL;
import joe.trader.client.traders.BNBTrader;
import joe.trader.client.traders.DOTTrader;
import joe.trader.client.traders.SOLTrader;
import joe.trader.client.traders.XRPTrader;
import joe.trader.entity.DbOrder;
import joe.trader.entity.DbPrice;
import joe.trader.repo.DbOrderRepo;
import joe.trader.repo.DbPriceRepo;
import joe.trader.repo.DbStatusRepo;
import joe.trader.util.Util;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class ExchangeDef {

  @Autowired
  private ApplicationContext context;

  @Value("${joe.params.alwaysBuy}")
  @Getter
  protected boolean alwaysBuy;

  @Value("${joe.params.alwaysSell}")
  @Getter
  protected boolean alwaysSell;

  @Value("${joe.use-binance:false}")
  private boolean useBinance;

  @Value("${joe.use-bybit:false}")
  private boolean useBybit;

  @Value("${joe.supported-assets}")
  private String supportedAssetsString;

  @Value("${joe.params.dryOrders}")
  @Getter
  protected boolean dryOrders;

  @Getter
  private Set<String> assetList;

  private Map<SYMBOL, Trader> traderMap;

  protected Util util;
  protected DbOrderRepo dbOrderRepo;
  protected DbStatusRepo dbStatusRepo;
  protected DbPriceRepo dbPriceRepo;
  protected RSISignal rsiSignal;
  protected MASignal maSignal;
  protected MACDSignal macdSignal;
  protected PriceSignal priceSignal;

  protected boolean initialized = false;

  public abstract void init();
  public abstract AssetBuyDTO checkBuyOrder(SYMBOL symbol);
  public abstract AssetSellDTO checkSellOrder(SYMBOL symbol);
  public abstract BigDecimal getLatestBalance();
  public abstract DbPrice getAssetPrice(SYMBOL symbol);

  @PostConstruct
  public void initAssetList() {
    if (supportedAssetsString != null && !supportedAssetsString.isEmpty()) {
      assetList = new HashSet<>(Arrays.asList(supportedAssetsString.split(",")));
      log.info("Initialized supported assets: {}", assetList);
    } else {
      // Default assets if not configured
      assetList = new HashSet<>(Arrays.asList("USDT", "SOL", "BNB", "BTC", "XRP"));
      log.info("Using default supported assets: {}", assetList);
    }
  }

  public final boolean isInit(){
    return initialized;
  }

  public final List<DbOrder> getOrders(EXCHANGE exchange, SYMBOL symbol) {
    return dbOrderRepo.findAllByAssetEqualsAndExchangeEqualsOrderByIdDesc(symbol.getName(), exchange.name());
  }

  public final void deleteAllOrders(EXCHANGE exchange) {
    dbOrderRepo.deleteAllByExchangeEquals(exchange.name());
  }

  public DbOrder openOrder(EXCHANGE exchange, SYMBOL symbol) {
    List<DbOrder> openOrders = dbOrderRepo.findBySoldPriceIsNullAndExchangeEqualsAndAssetEquals(exchange.name(), symbol.getName());
    log.info("open {} Orders: {}", symbol, openOrders.size());
    return openOrders.isEmpty() ? null : openOrders.getFirst();
  }

  public  void keepOrder(DbOrder openOrder, BigDecimal currentPrice) {
    openOrder.setLatestPrice(currentPrice);
    dbOrderRepo.save(openOrder);
  }

  public boolean isUseThisExchange() {
    return useBinance && this instanceof BinanceExchange || useBybit && this instanceof ByBitExchange;
  }

  public Map<SYMBOL, Trader> getTraderMap() {
    if(traderMap!=null) return traderMap;
    traderMap = Map.of(
        SYMBOL.SOLUSDT, context.getBean(SOLTrader.class),
        SYMBOL.BNBUSDT, context.getBean(BNBTrader.class),
        SYMBOL.XRPUSDT, context.getBean(XRPTrader.class),
        SYMBOL.DOTUSDT, context.getBean(DOTTrader.class)
    );
    return traderMap;
  }

  @Autowired
  public final void setUtil(Util util) {
    this.util = util;
  }
  @Autowired
  public final void setDbOrderRepo(DbOrderRepo dbOrderRepo) {
    this.dbOrderRepo = dbOrderRepo;
  }
  @Autowired
  public final void setDbStatusRepo(DbStatusRepo dbStatusRepo) {
    this.dbStatusRepo = dbStatusRepo;
  }
  @Autowired
  public final void setDbPriceRepo(DbPriceRepo dbPriceRepo) {
    this.dbPriceRepo = dbPriceRepo;
  }
  @Autowired
  public final void setRsiSignal(RSISignal rsiSignal) {
    this.rsiSignal = rsiSignal;
  }
  @Autowired
  public final void setMaSignal(MASignal maSignal) {
    this.maSignal = maSignal;
  }
  @Autowired
  public final void setMacdSignal(MACDSignal macdSignal) {
    this.macdSignal = macdSignal;
  }
  @Autowired
  public final void setPriceSignal(PriceSignal priceSignal) {
    this.priceSignal = priceSignal;
  }
}
