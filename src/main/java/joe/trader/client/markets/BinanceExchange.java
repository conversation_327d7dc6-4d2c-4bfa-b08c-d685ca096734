package joe.trader.client.markets;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.binance.connector.client.SpotClient;
import com.binance.connector.client.impl.SpotClientImpl;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.google.common.primitives.Doubles;

import joe.trader.client.dto.AssetBuyDTO;
import joe.trader.client.dto.AssetSellDTO;
import joe.trader.client.symbol.SYMBOL;
import joe.trader.entity.DbOrder;
import joe.trader.entity.DbPrice;
import joe.trader.entity.DbStatus;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class BinanceExchange extends ExchangeDef  {

  private EXCHANGE exchange = EXCHANGE.BINANCE;
  protected final Multimap<String, Object> assetInfo = ArrayListMultimap.create();
  @Getter
  private SpotClient binanceClient;

  @Value("${joe.api.key}")
  private String apiKey;

  @Value("${joe.api.secret}")
  private String secretKey;

  //flags
  @Value("${joe.params.deletePrevious}")
  private boolean deletePrevious;

  @Getter
  private double coefficientP = 0.003;

  @Getter
  private int uptrendStepsP = 3;

  @Getter
  private int usdAvailable = 100;

  @Getter
  private final int decimalsQuantity = 3;

  @Value("${joe.params.klines.short-term.interval:1s}")
  private String shortTermKlinesInterval;

  @Value("${joe.params.klines.short-term.limit:20}")
  private int shortTermKlinesLimit;

  @Value("${joe.params.klines.interval:1s}")
  private String longTermlinesInterval;

  @Value("${joe.params.klines.limit:60}")
  private int longTermKlinesLimit;

  public void init() {
    this.binanceClient = new SpotClientImpl(apiKey, secretKey);
    String result = binanceClient.createMarket().time();
    initialized = true;
    // Set trading parameters
    log.info("Binance API client initialized successfully, time: {}", result);
  }

  public BigDecimal getLatestBalance() {
    try {
      // If asset info is empty, fetch it first
      if (assetInfo.isEmpty()) {
        getAssetInfo();
      }

      // Use an array to allow modification in lambda
      final BigDecimal[] totalBalance = {BigDecimal.ZERO};

      // Calculate total balance by summing (quantity * price) for each asset
      assetInfo.asMap().forEach((symbol, symbolProps) -> {
        try {
          List<?> props = (List<?>) symbolProps;
          if (props.size() >= 2) {
            BigDecimal quantity = (BigDecimal) props.get(0);
            BigDecimal priceUSDT = (BigDecimal) props.get(1);
            BigDecimal assetValue = quantity.multiply(priceUSDT);
            totalBalance[0] = totalBalance[0].add(assetValue);

            log.debug("Asset: {}, Quantity: {}, Price: {}, Value: {}",
                symbol, quantity, priceUSDT, assetValue);
          }
        } catch (Exception e) {
          log.warn("Error processing balance for asset {}: {}", symbol, e.getMessage());
        }
      });

      log.info("Total balance in USDT: {}", totalBalance[0]);
      return totalBalance[0];
    } catch (Exception e) {
      log.error("Error calculating latest balance: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to calculate latest balance", e);
    }
  }

  public DbPrice getAssetPrice(SYMBOL symbol) {
    if (!isInit()) {
      throw new IllegalStateException("Spot client not initialized. Call init() first.");
    }

    if ("USDC".equals(symbol.getName()) || "USDT".equals(symbol.getName())) {
      return DbPrice.builder().price(BigDecimal.ONE).lowPrice(BigDecimal.ONE).highPrice(BigDecimal.ONE).build();
    }

    try {
      // Prepare request parameters
      Map<String, Object> parameters = new LinkedHashMap<>();
      parameters.put("symbol", symbol.getPair2());

      // Make API call to get current price
      final String info = binanceClient.createMarket().ticker(parameters);
//      final String data = new JSONObject(info).get("data").toString();

      // Parse the response
      DbPrice dbPrice = util.parseDBPrice(symbol.getName(), info);

      log.info("Current price for {}: {}", symbol, dbPrice);
      return dbPrice;
    } catch (Exception e) {
      log.error("Error retrieving price for asset {}: {}", symbol, e.getMessage(), e);
      throw new RuntimeException("Failed to retrieve price for asset " + symbol, e);
    }
  }


  public Multimap<String, Object> getAssetInfo() {
    if (!isInit()) {
      throw new IllegalStateException("Spot client not initialized. Call init() first.");
    }

    if (!isUseThisExchange()) {
      throw new IllegalStateException("Not using binance.");
    }

    log.info("Retrieving asset information and balances");
    try {
      // Clear existing asset info before updating
      assetInfo.clear();

      // Get account information from the exchange
      final String info = binanceClient.createTrade().account(new LinkedHashMap<>());
//      final String data = new JSONObject(info).get("data").toString();
      final String balances = new JSONObject(info).get("balances").toString();
      final JSONArray jsonArray = new JSONArray(balances);

      // Process each asset in the response
      jsonArray.forEach((item) -> {
        JSONObject jsonObject = (JSONObject) item;
        String asset = jsonObject.get("asset").toString();
        BigDecimal value = new BigDecimal(jsonObject.get("free").toString());

        // Only process assets we're interested in
        if (getAssetList().contains(asset)) {

          BigDecimal price = "USDT".equals(asset) ? BigDecimal.ONE : this.getAssetPrice(SYMBOL.valueOf(asset + "USDT")).getPrice();

          // Store both the balance and price in the multimap
          assetInfo.put(asset, value);  // Balance
          assetInfo.put(asset, price);  // Price

          log.info("Asset: {}, Balance: {}, Price: {}", asset, value, price);
        }
      });

      return assetInfo;
    } catch (Exception e) {
      log.error("Error retrieving asset information: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to retrieve asset information", e);
    }
  }



  /**
   * Retrieves historical klines (candlestick data) for an asset and persists them to the database
   *
   * @param dbStatus The database status object to associate with the klines
   * @param asset The asset symbol (e.g., "SOL", "BNB")
   * @param interval The interval for klines (e.g., "1s", "1m", "1h")
   * @param limit The maximum number of klines to retrieve
   * @throws IllegalArgumentException if any parameter is invalid
   * @throws RuntimeException if there's an error retrieving or persisting the klines
   */
  public void getAllKlinesHistorical(DbStatus dbStatus, String asset, String interval, int limit) {
    if (dbStatus == null) {
      throw new IllegalArgumentException("DbStatus cannot be null");
    }

    try {
      // Retrieve klines and store them in memory
      List<DbPrice> dbPrices = getAllKlinesHistoricalInMem(dbStatus.getId(), asset, interval, limit);

      // Persist the klines to the database
      util.persistsDbPrices(dbPrices);

      log.info("Retrieved and persisted {} klines for {} with interval {}",
          dbPrices.size(), asset, interval);
    } catch (Exception e) {
      log.error("Error retrieving historical klines for asset {}: {}", asset, e.getMessage(), e);
      throw new RuntimeException("Failed to retrieve historical klines for asset " + asset, e);
    }
  }

  /**
   * Retrieves historical klines (candlestick data) for an asset without persisting them
   *
   * @param idDbStatus The database status ID to associate with the klines
   * @param asset The asset symbol (e.g., "SOL", "BNB")
   * @param interval The interval for klines (e.g., "1s", "1m", "1h")
   * @param limit The maximum number of klines to retrieve
   * @return List of DbPrice objects containing the kline data
   * @throws IllegalArgumentException if any parameter is invalid
   * @throws RuntimeException if there's an error retrieving the klines
   */
  public List<DbPrice> getAllKlinesHistoricalInMem(long idDbStatus, String asset, String interval, int limit) {
    if (!isInit()) {
      throw new IllegalStateException("Spot client not initialized. Call init() first.");
    }

    if (asset == null || asset.isEmpty()) {
      throw new IllegalArgumentException("Asset symbol cannot be null or empty");
    }

    if (interval == null || interval.isEmpty()) {
      throw new IllegalArgumentException("Interval cannot be null or empty");
    }

    if (limit <= 0) {
      throw new IllegalArgumentException("Limit must be greater than zero");
    }

    try {
      // Prepare request parameters
      Map<String, Object> parameters = new LinkedHashMap<>();
      parameters.put("symbol", asset + "USDT");
      parameters.put("interval", interval);
      parameters.put("limit", limit);

      // Make API call to get klines
      final String info = binanceClient.createMarket().klines(parameters);
//      final String data = new JSONObject(info).get("data").toString();

      // Parse the response
      List<DbPrice> klines = util.parseKlines(idDbStatus, asset, info);

      log.debug("Retrieved {} klines for {} with interval {}", klines.size(), asset, interval);
      return klines;
    } catch (Exception e) {
      log.error("Error retrieving klines for asset {}: {}", asset, e.getMessage(), e);
      throw new RuntimeException("Failed to retrieve klines for asset " + asset, e);
    }
  }

  /**
   * Calculates the regression coefficient for an asset's price history
   *
   * @param dbStatus The database status object associated with the price data
   * @param asset The asset symbol (e.g., "SOL", "BNB")
   * @return The regression coefficient indicating price trend
   * @throws IllegalArgumentException if any parameter is invalid
   * @throws RuntimeException if there's an error calculating the regression
   */
  public BigDecimal getRegression(DbStatus dbStatus, String asset) {
    if (dbStatus == null) {
      throw new IllegalArgumentException("DbStatus cannot be null");
    }

    if (asset == null || asset.isEmpty()) {
      throw new IllegalArgumentException("Asset symbol cannot be null or empty");
    }

    try {
      // Retrieve price history from database
      List<DbPrice> dbPrices = dbPriceRepo.findByAssetAndIdDbStatusOrderByTimestampAsc(asset, dbStatus.getId());

      if (dbPrices == null || dbPrices.isEmpty()) {
        log.warn("No price data found for asset {} with status ID {}", asset, dbStatus.getId());
        return BigDecimal.ZERO; // Return neutral coefficient when no data is available
      }

      // Calculate regression coefficient
      BigDecimal coefficient = getCoefficientFromDbPrices(dbPrices);
      log.info("Regression coefficient for {}: {}", asset, coefficient);

      return coefficient;
    } catch (Exception e) {
      log.error("Error calculating regression for asset {}: {}", asset, e.getMessage(), e);
      throw new RuntimeException("Failed to calculate regression for asset " + asset, e);
    }
  }

  /**
   * Calculates the polynomial regression coefficient from a list of price data points
   *
   * @param dbPrices List of price data points
   * @return The regression coefficient
   * @throws IllegalArgumentException if the input list is invalid
   */
  private BigDecimal getCoefficientFromDbPrices(List<DbPrice> dbPrices) {
    if (dbPrices == null || dbPrices.isEmpty()) {
      throw new IllegalArgumentException("Price data list cannot be null or empty");
    }

    try {
      // Prepare x and y arrays for regression calculation
      List<Double> x = new ArrayList<>(dbPrices.size());
      List<Double> y = new ArrayList<>(dbPrices.size());

      // Fill arrays with data points
      double counter = 0;
      for (DbPrice dbPrice : dbPrices) {
        x.add(counter++);
        y.add(dbPrice.getPrice().doubleValue());
      }

      // Calculate polynomial regression
      return BigDecimal.valueOf(util.polyRegression(Doubles.toArray(x), Doubles.toArray(y))).setScale(
          4, RoundingMode.HALF_DOWN);
    } catch (Exception e) {
      log.error("Error calculating coefficient from price data: {}", e.getMessage(), e);
      throw new RuntimeException("Failed to calculate coefficient from price data", e);
    }
  }

  /**
   * Counts consecutive uptrend steps in the price history
   *
   * @param dbStatus The database status object associated with the price data
   * @param asset The asset symbol (e.g., "SOL", "BNB")
   * @return The number of consecutive uptrend steps
   * @throws IllegalArgumentException if any parameter is invalid
   */
  public int countUptrendSteps(DbStatus dbStatus, String asset) {
    if (dbStatus == null) {
      throw new IllegalArgumentException("DbStatus cannot be null");
    }

    if (asset == null || asset.isEmpty()) {
      throw new IllegalArgumentException("Asset symbol cannot be null or empty");
    }

    try {
      // Retrieve price history from database
      List<DbPrice> dbPrices = dbPriceRepo.findByAssetAndIdDbStatusOrderByTimestampAsc(asset, dbStatus.getId());

      if (dbPrices == null || dbPrices.size() < 2) {
        log.warn("Insufficient price data for asset {} to calculate uptrend steps", asset);
        return 0;
      }

      // Count consecutive price increases
      int counter = 0;
      for (int i = 1; i < dbPrices.size(); i++) {
        if (dbPrices.get(i).getPrice().compareTo(dbPrices.get(i-1).getPrice()) > 0) {
          counter++;
        } else {
          counter = 0; // Reset counter on any price decrease
        }
      }

      log.info("Consecutive uptrend steps for {}: {}", asset, counter);
      return counter;
    } catch (Exception e) {
      log.error("Error counting uptrend steps for asset {}: {}", asset, e.getMessage(), e);
      throw new RuntimeException("Failed to count uptrend steps for asset " + asset, e);
    }
  }

  /**
   * Calculates the short-term regression coefficient for an open order
   *
   * @param openOrder The open order to analyze
   * @param asset The asset symbol (e.g., "SOL", "BNB")
   * @return The regression coefficient for short-term trend analysis
   * @throws IllegalArgumentException if any parameter is invalid
   */
  public BigDecimal getRegression3m15(DbOrder openOrder, String asset) {
    if (openOrder == null) {
      throw new IllegalArgumentException("Open order cannot be null");
    }

    if (asset == null || asset.isEmpty()) {
      throw new IllegalArgumentException("Asset symbol cannot be null or empty");
    }

    try {
      // Get recent price data for short-term analysis
      List<DbPrice> dbPrices = getAllKlinesHistoricalInMem(openOrder.getIdDbStatus(), asset, shortTermKlinesInterval, shortTermKlinesLimit);

      if (dbPrices.isEmpty()) {
        log.warn("No recent price data found for asset {} to calculate short-term regression", asset);
        return BigDecimal.ZERO;
      }

      BigDecimal coefficient = getCoefficientFromDbPrices(dbPrices);
      log.info("Short-term regression coefficient for {}: {} (based on {} data points)",
          asset, coefficient, dbPrices.size());

      return coefficient;
    } catch (Exception e) {
      log.error("Error calculating short-term regression for asset {}: {}", asset, e.getMessage(), e);
      throw new RuntimeException("Failed to calculate short-term regression for asset " + asset, e);
    }
  }

  /**
   * Counts consecutive downtrend steps in the recent price history
   *
   * @param openOrder The open order to analyze
   * @param asset The asset symbol (e.g., "SOL", "BNB")
   * @return The number of consecutive downtrend steps
   * @throws IllegalArgumentException if any parameter is invalid
   */
  public int countDowntrendSteps(DbOrder openOrder, String asset) {
    if (openOrder == null) {
      throw new IllegalArgumentException("Open order cannot be null");
    }

    if (asset == null || asset.isEmpty()) {
      throw new IllegalArgumentException("Asset symbol cannot be null or empty");
    }

    try {
      // Get recent price data for trend analysis
      List<DbPrice> dbPrices = getAllKlinesHistoricalInMem(openOrder.getIdDbStatus(), asset, shortTermKlinesInterval, shortTermKlinesLimit);

      if (dbPrices == null || dbPrices.size() < 2) {
        log.warn("Insufficient price data for asset {} to calculate downtrend steps", asset);
        return 0;
      }

      // Count consecutive price decreases
      int counter = 0;
      for (int i = 1; i < dbPrices.size(); i++) {
        if (dbPrices.get(i).getPrice().compareTo(dbPrices.get(i-1).getPrice()) < 0) {
          counter++;
        } else {
          counter = 0; // Reset counter on any price increase
        }
      }

      log.info("Consecutive downtrend steps for {}: {}", asset, counter);
      return counter;
    } catch (Exception e) {
      log.error("Error counting downtrend steps for asset {}: {}", asset, e.getMessage(), e);
      throw new RuntimeException("Failed to count downtrend steps for asset " + asset, e);
    }
  }

  @Override
  public AssetBuyDTO checkBuyOrder(SYMBOL symbol) {

    if (!isUseThisExchange()) {
      log.debug("Not using binance.");
      return null;
    }
    if (!isInit()) {
      throw new IllegalStateException("Spot client not initialized. Call init() first.");
    }

    DbStatus dbStatus = new DbStatus(exchange);

    //get info
    Multimap<?, ?> assetInfo = getAssetInfo();
    BigDecimal latestBalance = getLatestBalance();

    BigDecimal previousBalance = (BigDecimal) util.ifNull(dbStatusRepo.getPreviousBalance(), latestBalance);
    log.info("assetInfo: {} latestBalance: {} previousBalance: {}", assetInfo, latestBalance, previousBalance);
    DbPrice dbPrice = getAssetPrice(symbol);
    priceSignal.setDbPrice(dbPrice);

    BigDecimal currentPrice = dbPrice.getPrice();
    //Persist
    dbStatus.setAssetInfo(assetInfo);
    dbStatus.setBalance(latestBalance);
    dbStatus.setPreviousBalance(previousBalance);
    dbStatus.setAssetDesc(String.valueOf(assetInfo));
    dbStatusRepo.save(dbStatus);

    boolean rsiBuy = rsiSignal.isOkToBuy(symbol, exchange, currentPrice);
    boolean maBuy = maSignal.isOkToBuy(symbol, exchange, currentPrice);
    boolean macdBuy = macdSignal.isOkToBuy(symbol, exchange, currentPrice);
    boolean priceBuy = priceSignal.isOkToBuy(symbol, exchange, currentPrice);
    boolean isUptrend = rsiSignal.isUptrend(symbol, exchange);

    getAllKlinesHistorical(dbStatus, symbol.getName(), longTermlinesInterval, longTermKlinesLimit);
    //Signal 1: positive polynomial coefficient 2
    BigDecimal coefficient = getRegression(dbStatus, symbol.getName());
    int uptrendSteps = countUptrendSteps(dbStatus, symbol.getName());
    boolean signal1 = coefficient.compareTo(BigDecimal.valueOf(getCoefficientP())) >= 0;
    boolean signal2 = uptrendSteps >= getUptrendStepsP();
    boolean signal4 = maBuy && macdBuy;

    if (!dryOrders) {
      if (isAlwaysBuy() || (signal1 && signal2) || rsiBuy || signal4) {

        if (openOrder(exchange, symbol) == null && isUptrend && priceBuy) {
          getTraderMap().get(symbol).buy(currentPrice, new BigDecimal(usdAvailable));
        } else {
          log.info("Not buying because isUptrend: {} buyPrice: {} or openOrder: {}", isUptrend, priceBuy, openOrder(exchange, symbol));
        }
      }
    }

    return AssetBuyDTO.builder().asset(symbol.getName())
        .rsiBuy(rsiBuy)
        .maBuy(maBuy)
        .macdBuy(macdBuy)
        .priceBuy(priceBuy)
        .uptrend(isUptrend)
        .alwaysBuy(isAlwaysBuy())
        .buy(false)
        .buyReason("")
        .dryOrders(dryOrders)
        .balance(latestBalance)
        .price(currentPrice)
        .lowPrice(dbPrice.getLowPrice())
        .highPrice(dbPrice.getHighPrice())
        .exchange(exchange.name())
        .build();
  }

  @Override
  public AssetSellDTO checkSellOrder(SYMBOL symbol) {

    DbOrder openOrder = openOrder(exchange, symbol);

    if (openOrder == null) {
      log.info(">>> No open order to sell");
      return null;
    }

    log.info("checkSellOrder : {}", openOrder);

    BigDecimal coefficient = getRegression3m15(openOrder, symbol.getName());
    int downtrendSteps = countDowntrendSteps(openOrder, symbol.getName());
    boolean signal1 = coefficient.compareTo(new BigDecimal("0.001")) < 0; //Downward trend
    boolean signal2 = downtrendSteps >= 3;
    int sellChecks = Optional.ofNullable(openOrder.getSellChecks()).orElse(0) + 1;

    DbPrice dbPrice = getAssetPrice(symbol);
    priceSignal.setDbPrice(dbPrice);
    BigDecimal currentPrice = dbPrice.getPrice();
    boolean sameOrHigher = currentPrice.compareTo(openOrder.getPrice()) >= 0;
    BigDecimal delta = currentPrice.subtract(openOrder.getPrice()).setScale(8, RoundingMode.HALF_UP);
    openOrder.setSoldPrice(currentPrice);
    openOrder.setSoldTimeStamp(new Date());
    openOrder.setSoldUSDValue(currentPrice.multiply(openOrder.getQuantity()));
    openOrder.setDelta(currentPrice.subtract(openOrder.getPrice()));

    boolean takeProfit1Perc = delta.divide(openOrder.getPrice(), 8, RoundingMode.HALF_UP).compareTo(BigDecimal.valueOf(0.01)) >= 0;
    boolean takeProfit5Perc = delta.divide(openOrder.getPrice(), 8, RoundingMode.HALF_UP).compareTo(BigDecimal.valueOf(0.05)) >= 0;
    boolean loss2Perc = currentPrice.compareTo(openOrder.getPrice().multiply(BigDecimal.valueOf(0.98))) < 0;

    log.info("checkSellOrder : *takeProfit5Perc: {} *takeProfit1Perc: {}", takeProfit5Perc, takeProfit1Perc);
    log.info("checkSellOrder : order {} *signal1: {} {} *signal2: {} *sameOrHigher: {} delta: {}", openOrder, signal1, coefficient, signal2, sameOrHigher, delta);

    boolean rsiSell = rsiSignal.isOkToSell(symbol, exchange, currentPrice);
    boolean maSell = maSignal.isOkToSell(symbol, exchange, currentPrice);
    boolean macdSell = macdSignal.isOkToSell(symbol, exchange, currentPrice);
    boolean priceSell = priceSignal.isOkToSell(symbol, exchange, currentPrice);
    boolean isDowntrend = rsiSignal.isDowntrend(symbol, exchange);

    boolean sell1 = isAlwaysSell();
    boolean sell2 = signal1 && signal2;
    boolean sell3 = takeProfit5Perc;
    boolean sell4 = takeProfit1Perc && (signal2 || signal1);
    boolean sell5 = System.currentTimeMillis() - openOrder.getTimestamp().getTime() > 4 * 60 * 60 * 1000;
    boolean keep1 = currentPrice.subtract(openOrder.getPrice()).doubleValue() < 0.30;
    boolean sell6 = sellChecks > 30;

    boolean sell7 = rsiSell || maSell || macdSell || priceSell;
    openOrder.setSellChecks(sellChecks);

    log.info("Selling order if any: sell1: {} sell2: {} sell3: {} sell4: {} sell5: {} sell6(disabled): {} sell7(RSI OK): {}", sell1, sell2, sell3, sell4, sell5, sell6, sell7);

    boolean sell = !keep1 && (sell1 || sell2 || sell3 || sell4 || sell5 || sell6 || sell7 || loss2Perc) && isDowntrend;

    if(!dryOrders) {
      if (sell) {
        log.info("Selling order: {} STOP LOSS 2%", openOrder);
        getTraderMap().get(symbol).sell(openOrder, currentPrice);
      } else {
        log.info("Keeping order: {}", openOrder);
        keepOrder(openOrder, currentPrice);
      }
    }

    return AssetSellDTO.builder().asset(symbol.getName())
        .rsiSell(rsiSell)
        .maSell(maSell)
        .macdSell(macdSell)
        .priceSell(priceSell)
        .downtrend(isDowntrend)
        .alwaysSell(isAlwaysSell())
        .sell(sell)
        .sellReason("")
        .dryOrders(dryOrders)
        .exchange(exchange.name())
        .balance(getLatestBalance())
        .price(currentPrice)
        .lowPrice(dbPrice.getLowPrice())
        .highPrice(dbPrice.getHighPrice())
        .build();
  }
}
