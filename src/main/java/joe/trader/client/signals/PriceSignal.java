package joe.trader.client.signals;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Optional;

import org.springframework.stereotype.Component;

import joe.trader.client.Signal;
import joe.trader.client.markets.EXCHANGE;
import joe.trader.client.symbol.SYMBOL;
import joe.trader.entity.DbPrice;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Provides buy/sell signals based on price thresholds calculated from historical price range.
 * Signal logic:
 * - Buy when price falls below 80% of the price range
 * - Sell when price rises above 90% of the price range
 */
@Component
@NoArgsConstructor
@Slf4j
public class PriceSignal implements Signal {

  private static final int LOW_THRESHOLD_PERCENT = 20;
  private static final int HIGH_BUY_THRESHOLD_PERCENT = 80;
  private static final int HIGH_SELL_THRESHOLD_PERCENT = 90;

  @Getter
  private DbPrice dbPrice;

  private double lowPrice;
  private double highPrice;
  private double low20Price;
  private double high80Price;
  private double high90Price;
  private boolean initialized = false;

  /**
   * Sets the reference price data and calculates threshold values.
   *
   * @param dbPrice The historical price data to base signals on
   * @throws IllegalArgumentException if dbPrice is null or has invalid data
   */
  public void setDbPrice(DbPrice dbPrice) {
    if (dbPrice == null) {
      throw new IllegalArgumentException("DbPrice cannot be null");
    }
    
    BigDecimal lowPriceBD = Optional.ofNullable(dbPrice.getLowPrice())
        .orElseThrow(() -> new IllegalArgumentException("Low price cannot be null"));
    
    BigDecimal highPriceBD = Optional.ofNullable(dbPrice.getHighPrice())
        .orElseThrow(() -> new IllegalArgumentException("High price cannot be null"));
    
    this.dbPrice = dbPrice;
    this.lowPrice = lowPriceBD.doubleValue();
    this.highPrice = highPriceBD.doubleValue();
    
    if (highPrice <= lowPrice) {
      throw new IllegalArgumentException("High price must be greater than low price");
    }
    
    double range = highPrice - lowPrice;
    double step = range / 100.0;
    
    this.low20Price = lowPrice + step * LOW_THRESHOLD_PERCENT;
    this.high80Price = lowPrice + step * HIGH_BUY_THRESHOLD_PERCENT;
    this.high90Price = lowPrice + step * HIGH_SELL_THRESHOLD_PERCENT;
    this.initialized = true;
    
    log.debug("Price thresholds calculated: low20={}, high80={}, high90={}", 
             low20Price, high80Price, high90Price);
  }

  /**
   * Determines if the asset is eligible for purchase at the given price.
   *
   * @param symbol The asset identifier
   * @param price The current price of the asset
   * @return true if the price is below the buy threshold, false otherwise
   */
  @Override
  public boolean isOkToBuy(SYMBOL symbol, EXCHANGE exchange, BigDecimal price) {
    validateInputs(symbol, price, "buy");
    
    if (!initialized) {
      log.warn("PriceSignal not initialized with price data for symbol {} on exchange {}", symbol.getName(), exchange.name());
      return false;
    }

    double priceValue = price.doubleValue();
    boolean result = priceValue < high80Price;

    log.info("PriceSignal buy evaluation: symbol={}, exchange={}, price={}, threshold={}, result={}",
             symbol.getName(), exchange.name(), price, high80Price, result);
    
    return result;
  }

  /**
   * Determines if the asset is eligible for sale at the given price.
   *
   * @param symbol The asset identifier
   * @param price The current price of the asset
   * @return true if the price is above the sell threshold, false otherwise
   */
  @Override
  public boolean isOkToSell(SYMBOL symbol, EXCHANGE exchange, BigDecimal price) {
    validateInputs(symbol, price, "sell");
    
    if (!initialized) {
      log.warn("PriceSignal not initialized with price data for symbol {} on exchange {}", symbol.getName(), exchange.name());
      return false;
    }

    double priceValue = price.doubleValue();
    boolean result = priceValue > high90Price;

    log.info("PriceSignal sell evaluation: symbol={}, exchange={}, price={}, threshold={}, result={}",
             symbol.getName(), exchange.name(), price, high90Price, result);
    
    return result;
  }

  /**
   * Returns information about the current price thresholds.
   *
   * @param asset The asset identifier
   * @return An array containing [lowPrice, low20Price, high80Price, high90Price, highPrice]
   * @throws IOException If information cannot be retrieved
   */
  public double[] getInfo(String asset, EXCHANGE exchange) throws IOException {
    if (!initialized) {
      log.warn("PriceSignal not initialized with price data for asset {} on exchange {}", asset, exchange.name());
      return new double[0];
    }
    
    return new double[] {lowPrice, low20Price, high80Price, high90Price, highPrice};
  }
  
  private void validateInputs(SYMBOL symbol, BigDecimal price, String operation) {
    if (symbol == null) {
      throw new IllegalArgumentException("Asset cannot be null");
    }
    
    if (price == null) {
      throw new IllegalArgumentException("Price cannot be null");
    }
  }
}