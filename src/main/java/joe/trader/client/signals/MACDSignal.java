package joe.trader.client.signals;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import joe.trader.client.Signal;
import joe.trader.client.markets.EXCHANGE;
import joe.trader.client.symbol.SYMBOL;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class MACDSignal implements Signal {

  @Value("${joe.taapi.io.key}")
  private String taapiiokey;

  @Override
  public boolean isOkToBuy(SYMBOL symbol, EXCHANGE exchange, BigDecimal price) {
    try {
      double[] macd = getInfo(symbol.getName(), exchange);
      return macd[2] > macd[1];
    } catch (IOException e) {
      log.error("Error getting MACD information", e);
      return false;
    }

  }

  @Override
  public boolean isOkToSell(SYMBOL symbol, EXCHANGE exchange, BigDecimal price) {
    try {
      double[] macd = getInfo(symbol.getName(), exchange);
      return macd[2] < macd[1];
    } catch (IOException e) {
      log.error("Error getting MACD information", e);
      return false;
    }
  }

  public double[] getInfo(String asset, EXCHANGE exchange) throws IOException {

    String result = get("https://api.taapi.io/macd?secret=" + taapiiokey + "&exchange=" + exchange.name().toLowerCase() + "&symbol=" + asset + "USDT&interval=1m&results=3&addResultTimestamp=true");

    // Parse the response
    log.info("MACD information: {}", result);

    if (result.contains("error")) {
      throw new IOException("Failed to get MACD information: " + result);
    }

    JSONObject jsonObject = new JSONObject(result);
    List<Object> data = jsonObject.getJSONArray("valueMACDHist").toList();
    double[] ma = new double[3];
    int i = 0;
    for (Object item : data) {
      ma[i++] = ((BigDecimal) item).doubleValue();
    }
    log.info("Current MACD value: {}", ma[2]);
    return ma;
  }


}