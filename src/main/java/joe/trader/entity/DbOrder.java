package joe.trader.entity;

import java.math.BigDecimal;
import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Temporal;
import jakarta.persistence.TemporalType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Entity
@Table(name = "dborder")
@Data
@Builder
@AllArgsConstructor
public class DbOrder {
  @Id
  @GeneratedValue(strategy= GenerationType.IDENTITY)
  private Long id;

  public DbOrder()
  {
    this.timestamp = new Date();
  }

  @Column(name="timestamp", nullable=false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date timestamp;

  @Column(name="asset")
  private String asset;

  @Column(name="exchange")
  private String exchange;

  @Column(name="price", nullable=false, precision = 15, scale = 8)
  private BigDecimal price;

  @Column(name="quantity", precision = 15, scale = 8)
  private BigDecimal quantity;

  @Column(name="usdValue", precision = 15, scale = 8)
  private BigDecimal usdValue;

  @Column(name="soldUsdValue", precision = 15, scale = 8)
  private BigDecimal soldUSDValue;

  @Column(name="side")
  private String side;

  @Column(name="soldPrice", precision = 15, scale = 8)
  private BigDecimal soldPrice;

  @Column(name="latestPrice", precision = 15, scale = 8)
  private BigDecimal latestPrice;

  @Column(name="soldTimeStamp")
  @Temporal(TemporalType.TIMESTAMP)
  private Date soldTimeStamp;

  @Column(name="delta", precision = 15, scale = 8)
  private BigDecimal delta;

  @Column(name="idDbStatus")
  private Long idDbStatus;

  @Column(name="sellChecks", columnDefinition = "int default 0")
  private Integer sellChecks;


}
