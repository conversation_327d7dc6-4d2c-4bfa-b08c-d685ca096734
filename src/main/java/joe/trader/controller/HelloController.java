package joe.trader.controller;

import java.util.Collection;
import java.util.List;
import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import joe.trader.client.JoeSpotClient;
import joe.trader.client.dto.AssetBuyDTO;
import joe.trader.client.dto.AssetSellDTO;
import joe.trader.client.markets.BinanceExchange;
import joe.trader.client.markets.ByBitExchange;
import joe.trader.client.markets.EXCHANGE;
import joe.trader.client.symbol.SYMBOL;
import joe.trader.client.traders.SOLTrader;
import joe.trader.entity.DbOrder;
import joe.trader.entity.DbStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
@RequiredArgsConstructor
public class HelloController {

  final JoeSpotClient joeSpotClient;
  final BinanceExchange binanceExchange;
  final ByBitExchange bybitExchange;

  final SOLTrader solTrader;

  @Value("${joe.use-binance:false}")
  boolean isUseBinance;

  @Value("${joe.use-bybit:false}")
  boolean isUseBybit;

  @GetMapping(path = "/init", produces = "application/json")
  public AssetBuyDTO[] init() {

    if (isUseBinance) {
      binanceExchange.init();
    }

    if (isUseBybit) {
      bybitExchange.init();
    }

    return new AssetBuyDTO[]{binanceExchange.checkBuyOrder(SYMBOL.SOLUSDT),
        bybitExchange.checkBuyOrder(SYMBOL.SOLUSDT)};
  }

  @GetMapping(path = "/SOLorders", produces = "application/json")
  public List<DbOrder> getSOLOrders() {

    List<DbOrder> binanceSOLOrders = binanceExchange.getOrders(EXCHANGE.BINANCE, SYMBOL.SOLUSDT);
    List<DbOrder> bybitSOLOrders = bybitExchange.getOrders(EXCHANGE.BYBIT, SYMBOL.SOLUSDT);

    return Stream.of(binanceSOLOrders, bybitSOLOrders).flatMap(Collection::stream).toList();
  }

  @GetMapping(path = "/dbStatus", produces = "application/json")
  public List<DbStatus> getDbStatus() {
    return joeSpotClient.getDbStatus();
  }

  @GetMapping(path = "/sumDbOrderDeltas", produces = "application/json")
  public String getDbDeltas() {
    return joeSpotClient.getDBDeltas();
  }

  @GetMapping(path = "/checkSellOrder", produces = "application/json")
  public AssetSellDTO[] checkSellOrder() {
     return new AssetSellDTO[]{binanceExchange.checkSellOrder(SYMBOL.SOLUSDT), bybitExchange.checkSellOrder(SYMBOL.SOLUSDT)};
  }

  @GetMapping(path = "/checkBuyOrderSOL", produces = "application/json")
  public String checkBuyOrderSOL() {
    return solTrader.buyerTask();
  }

  @DeleteMapping(path = "/orders")
  public void deleteAll() {
    binanceExchange.deleteAllOrders(EXCHANGE.BINANCE);
    bybitExchange.deleteAllOrders(EXCHANGE.BYBIT);
  }

  @DeleteMapping(path = "/dbStatus")
  public void deleteAllDbStatus() {
    joeSpotClient.deleteAllDbStatus();
  }

}