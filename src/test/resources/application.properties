spring.application.name=joe.bi
spring.jpa.hibernate.ddl-auto=update
spring.datasource.url=*********************************
spring.datasource.username=root
spring.datasource.password=meridian
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.show-sql=false
server.port=9090
spring.main.allow-circular-references=true

# Supported assets (comma-separated list)
joe.use-binance=true
joe.use-bybit=true
joe.supported-assets=USDT,SOL,BNB,BTC,XRP,DOT
joe.params.deletePrevious=true
joe.params.dryOrders=true
joe.params.useMarketPrice=true
joe.params.alwaysBuy=false
joe.params.alwaysSell=false
spring.task.scheduling.pool.size=10
sol.enabled=true
bnb.enabled=false
xrp.enabled=false
dot.enabled=false

# Klines parameters to buy
joe.params.klines.interval=1m
joe.params.klines.limit=15

# Klines parameters to sell
joe.params.klines.short-term.interval=1s
joe.params.klines.short-term.limit=30

trader.buyer.fixed-rate=300000
trader.seller.fixed-rate=120000

# Binance API credentials (replace with your actual keys)
joe.api.key=xu2ERhzBo6ZZJjUw2XJa4E4HLpZzjqZy2qBmdGWIN9PNUnlwbmjIvJbgn9WwmDUA
joe.api.secret=50i4bTY4t4IoeMyb7aKWbIFk8tbdyyClXhSeCqEeeCKkvfZdaHebYQZck8kfYgiN

# TA-API credentials (replace with your actual key)
joe.taapi.io.key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjbHVlIjoiNjgyMzc4MWQ4MDZmZjE2NTFlY2YzOWE4IiwiaWF0IjoxNzQ3MTU2NTU2LCJleHAiOjMzMjUxNjIwNTU2fQ.Yz7FksB6EmS3tCSy-yFF9ZMYNvi9I0Xul8Ut7r7bAKY

# Bybit API credentials (replace with your actual keys)
bybit.api.key=etMi0TByKyQOs9J5vsHAK50jI1Jzvo62kryV
bybit.api.secret=ISKkzYZ0RDJOdewl3O